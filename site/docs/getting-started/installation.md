---
id: installation
title: Installation
sidebar_position: 3
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

This guide will walk you through installing Envoy AI Gateway and its required components.

## Quick Installation with aigw CLI

The easiest way to install Envoy AI Gateway is using the `aigw install` command. This command will automatically install and configure all required components.

### Prerequisites

Before running the installer, ensure you have:

- A running Kubernetes cluster
- `kubectl` configured to access your cluster
- `helm` installed (version 3.0+)
- Sufficient permissions to create namespaces and install resources

### Basic Installation

To install Envoy AI Gateway with default settings:

```shell
aigw install
```

This will:

1. ✅ Check prerequisites (kubectl, helm, cluster connectivity)
2. 🌐 Install Envoy Gateway (if not already installed)
3. 📦 Install AI Gateway CRDs and controller
4. ⚙️ Configure Envoy Gateway for AI workloads
5. 🗄️ Install Redis for rate limiting
6. 🔍 Verify the installation

### Installation Options

The installer supports several options to customize the installation:

```shell
# Dry run to see what would be installed
aigw install --dry-run

# Skip Redis installation (disables rate limiting)
aigw install --skip-rate-limit

# Install to a custom namespace
aigw install --namespace my-ai-gateway

# Install a specific version
aigw install --version v0.2.0

# Set custom Helm values
aigw install --set key1=value1 --set key2=value2

# Use a custom values file
aigw install --values my-values.yaml

# Skip confirmation prompts
aigw install --skip-confirmation
```

### Troubleshooting

If the installation fails, the installer will:

- 🔄 Automatically attempt to rollback changes
- 💡 Provide troubleshooting suggestions
- 📋 Show helpful error messages and next steps

For more help:

- [Documentation](https://gateway.envoyproxy.io/ai-gateway/)
- [GitHub Issues](https://github.com/envoyproxy/ai-gateway/issues)
- [Community Slack](https://envoyproxy.slack.com/archives/C07Q4N24VAA)

### Uninstalling

To uninstall Envoy AI Gateway:

```shell
aigw uninstall
```

This will:

1. 🔍 Check what components are installed
2. 🗑️ Remove AI Gateway resources (CRDs, controller)
3. ⚙️ Remove Envoy Gateway configuration
4. 🌐 Remove Envoy Gateway (optional)
5. 🔍 Verify cleanup

#### Uninstall Options

```shell
# Dry run to see what would be removed
aigw uninstall --dry-run

# Keep Envoy Gateway installed (only remove AI Gateway)
aigw uninstall --keep-envoy-gateway

# Skip confirmation prompts
aigw uninstall --skip-confirmation

# Force uninstall even if some resources fail to be removed
aigw uninstall --force

# Uninstall from a custom namespace
aigw uninstall --namespace my-ai-gateway
```

## Manual Installation with Helm

The easiest way to install Envoy AI Gateway is using the Helm chart. First, install the AI Gateway Helm chart; this will install the CRDs as well. Once completed, wait for the deployment to be ready.

```shell
helm upgrade -i aieg oci://docker.io/envoyproxy/ai-gateway-helm \
    --version v0.0.0-latest \
    --namespace envoy-ai-gateway-system \
    --create-namespace

kubectl wait --timeout=2m -n envoy-ai-gateway-system deployment/ai-gateway-controller --for=condition=Available
```

### Installing CRDs serperately

If you want to manage the CRDs separately, install the CRD Helm chart (`ai-gateway-crds-helm`) which will install just the CRDs:

```shell
helm upgrade -i aieg-crd oci://docker.io/envoyproxy/ai-gateway-crds-helm \
    --version v0.0.0-latest \
    --namespace envoy-ai-gateway-system \
    --create-namespace
```

After the CRDs are installed, you can install the AI Gateway Helm chart without re-installing the CRDs by using the `--skip-crds` flag.

```shell
helm upgrade -i aieg oci://docker.io/envoyproxy/ai-gateway-helm \
    --version v0.0.0-latest \
    --namespace envoy-ai-gateway-system \
    --create-namespace \
    --skip-crds
```

## Configuring Envoy Gateway

After installing Envoy AI Gateway, apply the AI Gateway-specific configuration to Envoy Gateway, restart the deployment, and wait for it to be ready:

```shell
kubectl apply -f https://raw.githubusercontent.com/envoyproxy/ai-gateway/main/manifests/envoy-gateway-config/redis.yaml
kubectl apply -f https://raw.githubusercontent.com/envoyproxy/ai-gateway/main/manifests/envoy-gateway-config/config.yaml
kubectl apply -f https://raw.githubusercontent.com/envoyproxy/ai-gateway/main/manifests/envoy-gateway-config/rbac.yaml

kubectl rollout restart -n envoy-gateway-system deployment/envoy-gateway

kubectl wait --timeout=2m -n envoy-gateway-system deployment/envoy-gateway --for=condition=Available
```

Note that the redis configuration is only used for the rate limiting feature. If you don't need rate limiting, you can skip the redis configuration,
but you need to remove the relevant configuration in the `config.yaml` file as well.

:::tip Verify Installation

Check the status of the pods. All pods should be in the `Running` state with `Ready` status.

Check AI Gateway pods:

```shell
kubectl get pods -n envoy-ai-gateway-system
```

Check Envoy Gateway pods:

```shell
kubectl get pods -n envoy-gateway-system
```

:::

## Next Steps

After completing the installation:

- Continue to [Basic Usage](./basic-usage.md) to learn how to make your first request
- Or jump to [Connect Providers](./connect-providers) to set up OpenAI and AWS Bedrock integration
