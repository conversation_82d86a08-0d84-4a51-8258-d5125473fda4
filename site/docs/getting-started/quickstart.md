# Quick Start

Get Envoy AI Gateway up and running in minutes with the `aigw install` command.

## Prerequisites

Before you begin, ensure you have:

- A Kubernetes cluster (local or cloud)
- `kubectl` configured to access your cluster
- `helm` installed (version 3.0+)
- Cluster admin permissions

## Step 1: Install the CLI

Download and install the `aigw` CLI:

```shell
# Download the latest release
curl -L https://github.com/envoyproxy/ai-gateway/releases/latest/download/aigw-linux-amd64 -o aigw

# Make it executable
chmod +x aigw

# Move to PATH
sudo mv aigw /usr/local/bin/
```

## Step 2: Install AI Gateway

Run the installer with a single command:

```shell
aigw install
```

You'll see a beautiful installation progress with real-time feedback:

```text
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               🚀 Envoy AI Gateway Installer                  ║
║                                                              ║
║   This installer will set up Envoy AI Gateway and           ║
║   configure Envoy Gateway for AI workloads.                 ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

📋 Checking prerequisites...
  🔍 Checking kubectl...
  ✅ kubectl is available
  🔍 Checking cluster connectivity...
  ✅ Cluster connectivity verified
  🔍 Checking Helm...
  ✅ Helm is available
✅ Prerequisites check completed

🌐 Installing Envoy Gateway...
  📦 Installing Envoy Gateway with Helm...
  ✅ Envoy Gateway installed
  ⏳ Waiting for Envoy Gateway to be ready...
  ✅ Envoy Gateway is ready
✅ Envoy Gateway installation completed

📦 Installing AI Gateway...
  📋 Installing AI Gateway CRDs...
  ✅ AI Gateway CRDs installed
  🎛️ Installing AI Gateway controller...
  ✅ AI Gateway controller installed
  ⏳ Waiting for AI Gateway to be ready...
  ✅ AI Gateway is ready
✅ AI Gateway installation completed

⚙️ Configuring Envoy Gateway...
  🗄️ Installing Redis for rate limiting...
  ✅ Redis configuration applied
  ⚙️ Applying Envoy Gateway configuration...
  ✅ Envoy Gateway configuration applied
  🔐 Applying RBAC configuration...
  ✅ RBAC configuration applied
  🔄 Restarting Envoy Gateway...
  ✅ Envoy Gateway restart initiated
  ⏳ Waiting for Envoy Gateway to be ready...
  ✅ Envoy Gateway is ready
✅ Envoy Gateway configuration completed

🔍 Verifying installation...
  🔍 Checking AI Gateway pods...
  ✅ AI Gateway pods are running (1/1)
  🔍 Checking Envoy Gateway pods...
  ✅ Envoy Gateway pods are running (1/1)
  🔍 Checking Redis pods...
  ✅ Redis pods are running (1/1)
  🔍 Checking services...
  ✅ AI Gateway service is accessible
  ✅ Envoy Gateway service is accessible
✅ Installation verification completed

╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               ✅ Installation Complete!                      ║
║                                                              ║
║   Envoy AI Gateway has been successfully installed and      ║
║   configured. You can now deploy AI Gateway resources.      ║
║                                                              ║
║   Next steps:                                                ║
║   • Check the getting-started guide for examples            ║
║   • Configure your AI providers (OpenAI, AWS Bedrock, etc.) ║
║   • Deploy your first AIGatewayRoute                        ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
```

## Step 3: Verify Installation

Check that all components are running:

```shell
# Check AI Gateway pods
kubectl get pods -n envoy-ai-gateway-system

# Check Envoy Gateway pods
kubectl get pods -n envoy-gateway-system

# Check Redis pods (if rate limiting is enabled)
kubectl get pods -n redis-system
```

## Step 4: Deploy Your First AI Route

Create a simple AI Gateway route:

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: my-first-route
  namespace: default
spec:
  targetRef:
    group: gateway.networking.k8s.io
    kind: HTTPRoute
    name: my-http-route
  rules:
  - backendRefs:
    - group: gateway.envoyproxy.io
      kind: AIServiceBackend
      name: openai-backend
```

Apply it:

```shell
kubectl apply -f my-first-route.yaml
```

## Customization Options

The installer supports many customization options:

```shell
# Install without rate limiting
aigw install --skip-rate-limit

# Install to a custom namespace
aigw install --namespace my-namespace

# Install a specific version
aigw install --version v0.2.0

# Use custom Helm values
aigw install --values my-values.yaml

# Set individual values
aigw install --set controller.replicas=2 --set redis.enabled=false
```

## Troubleshooting

If something goes wrong, the installer provides helpful guidance:

- **Automatic rollback**: Failed installations are automatically rolled back
- **Detailed error messages**: Clear explanations of what went wrong
- **Troubleshooting tips**: Specific suggestions based on the error type
- **Help resources**: Links to documentation and community support

## Next Steps

Now that AI Gateway is installed:

1. **Configure AI Providers**: Set up [OpenAI](../providers/openai.md) or [AWS Bedrock](../providers/aws-bedrock.md)
2. **Create Routes**: Learn about [AIGatewayRoute](../api/aigatewayroute.md) configuration
3. **Set up Rate Limiting**: Configure [rate limiting](../features/rate-limiting.md) for your APIs
4. **Monitor Usage**: Set up [observability](../features/observability.md) and monitoring

For more detailed configuration options, see the [Installation Guide](./installation.md).
