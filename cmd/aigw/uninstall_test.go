// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Copyright Envoy AI Gateway Authors.
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at.
// the root of the repo.

package main

import (
	"bytes"
	"context"
	"io"
	"log/slog"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUninstallCommand(t *testing.T) {
	tests := []struct {
		name           string
		cmd            cmdUninstall
		expectError    bool
		expectedOutput []string
	}{
		{
			name: "dry run basic uninstall",
			cmd: cmdUninstall{
				DryRun:           true,
				SkipConfirmation: true,
				Namespace:        "test-namespace",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Envoy AI Gateway Uninstaller",
				"Checking installed components",
				"Uninstallation Complete",
			},
		},
		{
			name: "dry run keep envoy gateway",
			cmd: cmdUninstall{
				DryRun:           true,
				SkipConfirmation: true,
				KeepEnvoyGateway: true,
				Namespace:        "test-namespace",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Envoy AI Gateway Uninstaller",
				"Uninstallation Complete",
			},
		},
		{
			name: "force uninstall",
			cmd: cmdUninstall{
				DryRun:           true,
				SkipConfirmation: true,
				Force:            true,
				Namespace:        "test-namespace",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Uninstallation Complete",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var stdout, stderr bytes.Buffer

			err := uninstall(context.Background(), tt.cmd, &stdout, &stderr)

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			output := stdout.String()
			for _, expected := range tt.expectedOutput {
				assert.Contains(t, output, expected, "Expected output to contain: %s", expected)
			}
		})
	}
}

func TestUninstallOrchestrator_PrintFunctions(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
	}

	// Test print functions.
	uninstaller.printStep("🔍", "Test step")
	uninstaller.printSuccess("Test success")
	uninstaller.printError("Test error")
	uninstaller.printWarning("Test warning")
	uninstaller.printInfo("Test info")

	output := stdout.String()
	assert.Contains(t, output, "🔍")
	assert.Contains(t, output, "Test step")
	assert.Contains(t, output, "✅")
	assert.Contains(t, output, "Test success")
	assert.Contains(t, output, "❌")
	assert.Contains(t, output, "Test error")
	assert.Contains(t, output, "⚠️")
	assert.Contains(t, output, "Test warning")
	assert.Contains(t, output, "ℹ️")
	assert.Contains(t, output, "Test info")
}

func TestUninstallOrchestrator_RunCommand(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
		logger: slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{})),
	}

	// Test dry run command.
	output, err := uninstaller.runCommand(context.Background(), "echo", "test")
	require.NoError(t, err)
	assert.Empty(t, output) // Dry run should return empty output.

	// Test actual command (non-dry run).
	uninstaller.cmd.DryRun = false
	output, err = uninstaller.runCommand(context.Background(), "echo", "test")
	require.NoError(t, err)
	assert.Contains(t, output, "test")
}

func TestUninstallOrchestrator_CheckInstalledComponents(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
		logger: slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{})),
	}

	// Test checking components (dry run mode will return empty).
	components, err := uninstaller.checkInstalledComponents(context.Background())
	require.NoError(t, err)
	assert.Empty(t, components) // Dry run should return no components.

	output := stdout.String()
	assert.Contains(t, output, "Checking installed components")
	assert.Contains(t, output, "No AI Gateway components found")
}

func TestUninstallOrchestrator_IsHelmReleaseInstalled(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
		logger: slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{})),
	}

	// Test checking Helm release (dry run mode will return false).
	installed := uninstaller.isHelmReleaseInstalled(context.Background(), "test-release", "test-namespace")
	assert.False(t, installed) // Dry run should return false.
}

func TestUninstallOrchestrator_AskConfirmation(_ *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd:    cmdUninstall{},
		stdout: &stdout,
		stderr: &stderr,
	}

	// Note: This test doesn't actually test user input since that would require
	// mocking stdin. In a real scenario, you might want to use dependency injection
	// to make this more testable.

	// Just test that the function exists and can be called.
	// In a real test environment, you would mock the input.
	_ = uninstaller.askConfirmation
}

func TestUninstallOrchestrator_RemoveYAMLFromFile(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
		logger: slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{})),
	}

	// Test removing YAML file (dry run mode).
	err := uninstaller.removeYAMLFromFile(context.Background(), "test-file.yaml")
	require.NoError(t, err) // Dry run should succeed.
}

func TestUninstallOrchestrator_UninstallHelmRelease(t *testing.T) {
	var stdout, stderr bytes.Buffer

	uninstaller := &uninstallOrchestrator{
		cmd: cmdUninstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
		logger: slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{})),
	}

	// Test uninstalling Helm release (dry run mode).
	err := uninstaller.uninstallHelmRelease(context.Background(), "test-release", "test-namespace")
	require.NoError(t, err) // Dry run should succeed.

	output := stdout.String()
	assert.Contains(t, output, "Uninstalling Helm release test-release")
	assert.Contains(t, output, "Helm release test-release uninstalled")
}
