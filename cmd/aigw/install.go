// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Copyright Envoy AI Gateway Authors.
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at.
// the root of the repo.

package main

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/fatih/color"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// findConfigFile tries to find a configuration file in common locations.
func findConfigFile(filename string) (string, error) {
	// Try different possible paths
	possiblePaths := []string{
		filename, // Current directory
		filepath.Join("manifests", "envoy-gateway-config", filename),             // From project root
		filepath.Join("..", "..", "manifests", "envoy-gateway-config", filename), // From cmd/aigw
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("configuration file %s not found in any of these locations: %v", filename, possiblePaths)
}

// install implements subCmd[cmdInstall]. This function orchestrates the installation
// of Envoy AI Gateway and configures Envoy Gateway.
func install(ctx context.Context, cmd cmdInstall, stdout, stderr io.Writer) error {
	stderrLogger := slog.New(slog.NewTextHandler(stderr, &slog.HandlerOptions{}))
	if !cmd.Debug {
		stderrLogger = slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{}))
	}

	// Create Kubernetes client.
	k8sClient, err := createKubernetesClient()
	if err != nil {
		return fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	// Create installation orchestrator.
	installer := &installOrchestrator{
		cmd:       cmd,
		stdout:    stdout,
		stderr:    stderr,
		logger:    stderrLogger,
		k8sClient: k8sClient,
	}

	// Run the installation process.
	return installer.run(ctx)
}

// installOrchestrator manages the installation process.
type installOrchestrator struct {
	cmd       cmdInstall
	stdout    io.Writer
	stderr    io.Writer
	logger    *slog.Logger
	k8sClient kubernetes.Interface

	// Track installation state for rollback.
	installedComponents []string
}

// run executes the installation process.
func (i *installOrchestrator) run(ctx context.Context) error {
	if i.cmd.DryRun {
		i.printWarning("Dry run mode - no changes will be made")
	}

	// Print welcome banner.
	i.printBanner()

	// Set up error handling with rollback.
	defer func() {
		if r := recover(); r != nil {
			i.printError(fmt.Sprintf("Installation failed with panic: %v", r))
			_ = i.rollbackInstallation(ctx)
		}
	}()

	// Step 1: Check prerequisites.
	if err := i.checkPrerequisites(ctx); err != nil {
		return i.handleError("prerequisite check failed", err, false)
	}

	// Step 2: Install Envoy Gateway (if not already installed).
	if err := i.installEnvoyGateway(ctx); err != nil {
		return i.handleError("Envoy Gateway installation failed", err, true)
	}

	// Step 3: Install AI Gateway.
	if err := i.installAIGateway(ctx); err != nil {
		return i.handleError("AI Gateway installation failed", err, true)
	}

	// Step 4: Configure Envoy Gateway.
	if err := i.configureEnvoyGateway(ctx); err != nil {
		return i.handleError("Envoy Gateway configuration failed", err, true)
	}

	// Step 5: Verify installation.
	if err := i.verifyInstallation(ctx); err != nil {
		return i.handleError("installation verification failed", err, false)
	}

	i.printCompletionBanner()
	return nil
}

// checkPrerequisites verifies that all required tools and conditions are met.
func (i *installOrchestrator) checkPrerequisites(ctx context.Context) error {
	i.printStep("📋", "Checking prerequisites...")

	// Check kubectl availability.
	if err := i.checkKubectl(ctx); err != nil {
		return err
	}

	// Check cluster connectivity.
	if err := i.checkClusterConnectivity(ctx); err != nil {
		return err
	}

	// Check Helm availability.
	if err := i.checkHelm(ctx); err != nil {
		return err
	}

	i.printSuccess("Prerequisites check completed")
	i.logger.Info("Prerequisites check completed")
	return nil
}

// installEnvoyGateway installs Envoy Gateway if not already installed.
func (i *installOrchestrator) installEnvoyGateway(ctx context.Context) error {
	i.printStep("🌐", "Installing Envoy Gateway...")

	// Check if Envoy Gateway is already installed.
	if err := i.checkEnvoyGatewayInstalled(ctx); err == nil {
		i.printSuccess("Envoy Gateway is already installed")
		return nil
	}

	// Install Envoy Gateway using Helm.
	if err := i.installEnvoyGatewayHelm(ctx); err != nil {
		return err
	}

	// Wait for Envoy Gateway to be ready.
	if err := i.waitForEnvoyGatewayReady(ctx); err != nil {
		return err
	}

	i.printSuccess("Envoy Gateway installation completed")
	i.logger.Info("Envoy Gateway installation completed")
	return nil
}

// installAIGateway installs the AI Gateway using Helm.
func (i *installOrchestrator) installAIGateway(ctx context.Context) error {
	i.printStep("📦", "Installing AI Gateway...")

	// Install CRDs first.
	if err := i.installAIGatewayCRDs(ctx); err != nil {
		return err
	}

	// Install AI Gateway controller.
	if err := i.installAIGatewayController(ctx); err != nil {
		return err
	}

	// Wait for deployment to be ready.
	if err := i.waitForAIGatewayReady(ctx); err != nil {
		return err
	}

	i.printSuccess("AI Gateway installation completed")
	i.logger.Info("AI Gateway installation completed")
	return nil
}

// configureEnvoyGateway applies the necessary configuration to Envoy Gateway.
func (i *installOrchestrator) configureEnvoyGateway(ctx context.Context) error {
	i.printStep("⚙️", "Configuring Envoy Gateway...")

	// Apply Redis configuration (if not skipped).
	if !i.cmd.SkipRateLimit {
		if err := i.applyRedisConfiguration(ctx); err != nil {
			return err
		}
	}

	// Apply Envoy Gateway configuration.
	if err := i.applyEnvoyGatewayConfig(ctx); err != nil {
		return err
	}

	// Apply RBAC configuration.
	if err := i.applyRBACConfiguration(ctx); err != nil {
		return err
	}

	// Restart Envoy Gateway deployment.
	if err := i.restartEnvoyGateway(ctx); err != nil {
		return err
	}

	// Wait for Envoy Gateway to be ready.
	if err := i.waitForEnvoyGatewayReady(ctx); err != nil {
		return err
	}

	i.printSuccess("Envoy Gateway configuration completed")
	i.logger.Info("Envoy Gateway configuration completed")
	return nil
}

// verifyInstallation checks that all components are running correctly.
func (i *installOrchestrator) verifyInstallation(ctx context.Context) error {
	i.printStep("🔍", "Verifying installation...")

	// Check AI Gateway pods status.
	if err := i.verifyAIGatewayPods(ctx); err != nil {
		return err
	}

	// Check Envoy Gateway pods status.
	if err := i.verifyEnvoyGatewayPods(ctx); err != nil {
		return err
	}

	// Check Redis pods status (if installed).
	if !i.cmd.SkipRateLimit {
		if err := i.verifyRedisPods(ctx); err != nil {
			return err
		}
	}

	// Verify services are accessible.
	if err := i.verifyServices(ctx); err != nil {
		return err
	}

	i.printSuccess("Installation verification completed")
	i.logger.Info("Installation verification completed")
	return nil
}

// createKubernetesClient creates a Kubernetes client using the default kubeconfig.
func createKubernetesClient() (kubernetes.Interface, error) {
	config, err := clientcmd.BuildConfigFromFlags("", clientcmd.RecommendedHomeFile)
	if err != nil {
		return nil, fmt.Errorf("failed to build kubeconfig: %w", err)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	return clientset, nil
}

// runCommand executes a command and returns the output.
func (i *installOrchestrator) runCommand(ctx context.Context, name string, args ...string) (string, error) {
	if i.cmd.DryRun {
		i.logger.Info("Dry run: would execute command", "command", name, "args", args)
		return "", nil
	}

	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), fmt.Errorf("command failed: %s %v: %w\nOutput: %s", name, args, err, string(output))
	}
	return string(output), nil
}

// applyYAMLContent applies YAML content using kubectl.
func (i *installOrchestrator) applyYAMLContent(ctx context.Context, yamlContent string) error {
	if i.cmd.DryRun {
		i.logger.Info("Dry run: would apply YAML content", "content", yamlContent[:100]+"...")
		return nil
	}

	// Create a temporary file with the YAML content.
	tmpFile, err := os.CreateTemp("", "aigw-config-*.yaml")
	if err != nil {
		return fmt.Errorf("failed to create temporary file: %w", err)
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// Write the YAML content to the temporary file.
	if _, err := tmpFile.WriteString(yamlContent); err != nil {
		return fmt.Errorf("failed to write YAML content: %w", err)
	}

	// Close the file so kubectl can read it.
	if err := tmpFile.Close(); err != nil {
		return fmt.Errorf("failed to close temporary file: %w", err)
	}

	// Apply the YAML file.
	args := []string{"apply", "-f", tmpFile.Name()}
	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("failed to apply YAML: %w", err)
	}

	i.logger.Info("Applied YAML content", "output", output)
	return nil
}

// printStep prints a step with an icon and message.
func (i *installOrchestrator) printStep(icon, message string) {
	fmt.Fprintf(i.stdout, "%s %s\n", icon, color.CyanString(message))
}

// printSuccess prints a success message.
func (i *installOrchestrator) printSuccess(message string) {
	fmt.Fprintf(i.stdout, "✅ %s\n", color.GreenString(message))
}

// printError prints an error message.
func (i *installOrchestrator) printError(message string) {
	fmt.Fprintf(i.stdout, "❌ %s\n", color.RedString(message))
}

// printWarning prints a warning message.
func (i *installOrchestrator) printWarning(message string) {
	fmt.Fprintf(i.stdout, "⚠️  %s\n", color.YellowString(message))
}

// printBanner prints a welcome banner.
func (i *installOrchestrator) printBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               🚀 Envoy AI Gateway Installer                  ║
║                                                              ║
║   This installer will set up Envoy AI Gateway and           ║
║   configure Envoy Gateway for AI workloads.                 ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`
	fmt.Fprint(i.stdout, color.CyanString(banner))
}

// printCompletionBanner prints a completion banner.
func (i *installOrchestrator) printCompletionBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               ✅ Installation Complete!                      ║
║                                                              ║
║   Envoy AI Gateway has been successfully installed and      ║
║   configured. You can now deploy AI Gateway resources.      ║
║                                                              ║
║   Next steps:                                                ║
║   • Check the getting-started guide for examples            ║
║   • Configure your AI providers (OpenAI, AWS Bedrock, etc.) ║
║   • Deploy your first AIGatewayRoute                        ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`
	fmt.Fprint(i.stdout, color.GreenString(banner))
}

// checkKubectl verifies that kubectl is available and working.
func (i *installOrchestrator) checkKubectl(ctx context.Context) error {
	i.printStep("  🔍", "Checking kubectl...")

	// Check if kubectl is available.
	if _, err := exec.LookPath("kubectl"); err != nil {
		i.printError("kubectl not found in PATH")
		return fmt.Errorf("kubectl is required but not found in PATH")
	}

	// Check kubectl version.
	output, err := i.runCommand(ctx, "kubectl", "version", "--client", "--output=yaml")
	if err != nil {
		i.printError("Failed to get kubectl version")
		return fmt.Errorf("failed to get kubectl version: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("kubectl version", "output", output)
	}

	i.printSuccess("kubectl is available")
	return nil
}

// checkClusterConnectivity verifies that we can connect to the Kubernetes cluster.
func (i *installOrchestrator) checkClusterConnectivity(ctx context.Context) error {
	i.printStep("  🔍", "Checking cluster connectivity...")

	// Try to get cluster info.
	_, err := i.runCommand(ctx, "kubectl", "cluster-info")
	if err != nil {
		i.printError("Cannot connect to Kubernetes cluster")
		return fmt.Errorf("failed to connect to Kubernetes cluster: %w", err)
	}

	i.printSuccess("Cluster connectivity verified")
	return nil
}

// checkHelm verifies that Helm is available and working.
func (i *installOrchestrator) checkHelm(ctx context.Context) error {
	i.printStep("  🔍", "Checking Helm...")

	// Check if helm is available.
	if _, err := exec.LookPath("helm"); err != nil {
		i.printError("Helm not found in PATH")
		return fmt.Errorf("helm is required but not found in PATH")
	}

	// Check helm version.
	output, err := i.runCommand(ctx, "helm", "version", "--short")
	if err != nil {
		i.printError("Failed to get Helm version")
		return fmt.Errorf("failed to get Helm version: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("Helm version", "output", output)
	}

	i.printSuccess("Helm is available")
	return nil
}

// checkEnvoyGatewayInstalled checks if Envoy Gateway is already installed.
func (i *installOrchestrator) checkEnvoyGatewayInstalled(ctx context.Context) error {
	args := []string{
		"get", "namespace", "envoy-gateway-system",
		"--ignore-not-found=true",
		"--no-headers",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("failed to check envoy-gateway-system namespace: %w", err)
	}

	if strings.TrimSpace(output) == "" {
		return fmt.Errorf("envoy-gateway-system namespace not found")
	}

	// Check if Envoy Gateway deployment exists.
	args = []string{
		"get", "deployment", "envoy-gateway",
		"-n", "envoy-gateway-system",
		"--ignore-not-found=true",
		"--no-headers",
	}

	output, err = i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("failed to check envoy-gateway deployment: %w", err)
	}

	if strings.TrimSpace(output) == "" {
		return fmt.Errorf("envoy-gateway deployment not found")
	}

	return nil
}

// installEnvoyGatewayHelm installs Envoy Gateway using Helm.
func (i *installOrchestrator) installEnvoyGatewayHelm(ctx context.Context) error {
	i.printStep("  📦", "Installing Envoy Gateway with Helm...")

	// Add Envoy Gateway Helm repository.
	if err := i.addEnvoyGatewayHelmRepo(ctx); err != nil {
		return err
	}

	// Install Envoy Gateway.
	args := []string{
		"upgrade", "--install", "eg",
		"oci://docker.io/envoyproxy/gateway-helm",
		"--version", "v1.2.4",
		"--namespace", "envoy-gateway-system",
		"--create-namespace",
	}

	if i.cmd.DryRun {
		args = append(args, "--dry-run")
	}

	output, err := i.runCommand(ctx, "helm", args...)
	if err != nil {
		i.printError("Failed to install Envoy Gateway")
		return fmt.Errorf("failed to install Envoy Gateway: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("Envoy Gateway installation output", "output", output)
	}

	i.printSuccess("Envoy Gateway installed")
	i.installedComponents = append(i.installedComponents, "envoy-gateway")
	return nil
}

// addEnvoyGatewayHelmRepo adds the Envoy Gateway Helm repository.
func (i *installOrchestrator) addEnvoyGatewayHelmRepo(ctx context.Context) error {
	i.printStep("  📋", "Adding Envoy Gateway Helm repository...")

	// Check if repo already exists.
	output, err := i.runCommand(ctx, "helm", "repo", "list", "-o", "json")
	if err == nil && !i.cmd.DryRun {
		if strings.Contains(output, "oci://docker.io/envoyproxy") {
			i.printSuccess("Envoy Gateway Helm repository already exists")
			return nil
		}
	}

	// Note: OCI repositories don't need to be added explicitly.
	i.printSuccess("Envoy Gateway Helm repository ready")
	return nil
}

// installAIGatewayCRDs installs the AI Gateway CRDs using Helm.
func (i *installOrchestrator) installAIGatewayCRDs(ctx context.Context) error {
	i.printStep("  📋", "Installing AI Gateway CRDs...")

	args := []string{
		"upgrade", "--install", "aieg-crd",
		"oci://docker.io/envoyproxy/ai-gateway-crds-helm",
		"--version", i.cmd.Version,
		"--namespace", i.cmd.Namespace,
		"--create-namespace",
	}

	if i.cmd.DryRun {
		args = append(args, "--dry-run")
	}

	output, err := i.runCommand(ctx, "helm", args...)
	if err != nil {
		i.printError("Failed to install AI Gateway CRDs")
		return fmt.Errorf("failed to install AI Gateway CRDs: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("AI Gateway CRDs installation output", "output", output)
	}

	i.printSuccess("AI Gateway CRDs installed")
	i.installedComponents = append(i.installedComponents, "ai-gateway-crds")
	return nil
}

// installAIGatewayController installs the AI Gateway controller using Helm.
func (i *installOrchestrator) installAIGatewayController(ctx context.Context) error {
	i.printStep("  🎛️", "Installing AI Gateway controller...")

	args := []string{
		"upgrade", "--install", "aieg",
		"oci://docker.io/envoyproxy/ai-gateway-helm",
		"--version", i.cmd.Version,
		"--namespace", i.cmd.Namespace,
		"--create-namespace",
		"--skip-crds", // CRDs are already installed.
	}

	// Add custom values if provided.
	if i.cmd.Values != "" {
		args = append(args, "--values", i.cmd.Values)
	}

	// Add set values.
	for _, setValue := range i.cmd.Set {
		args = append(args, "--set", setValue)
	}

	if i.cmd.DryRun {
		args = append(args, "--dry-run")
	}

	output, err := i.runCommand(ctx, "helm", args...)
	if err != nil {
		i.printError("Failed to install AI Gateway controller")
		return fmt.Errorf("failed to install AI Gateway controller: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("AI Gateway controller installation output", "output", output)
	}

	i.printSuccess("AI Gateway controller installed")
	i.installedComponents = append(i.installedComponents, "ai-gateway-controller")
	return nil
}

// waitForAIGatewayReady waits for the AI Gateway deployment to be ready.
func (i *installOrchestrator) waitForAIGatewayReady(ctx context.Context) error {
	if i.cmd.DryRun {
		i.printStep("  ⏳", "Would wait for AI Gateway to be ready")
		return nil
	}

	i.printStep("  ⏳", "Waiting for AI Gateway to be ready...")

	args := []string{
		"wait", "--timeout=2m",
		"-n", i.cmd.Namespace,
		"deployment/ai-gateway-controller",
		"--for=condition=Available",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printError("AI Gateway deployment is not ready")
		return fmt.Errorf("AI Gateway deployment is not ready: %w", err)
	}

	i.logger.Info("AI Gateway deployment ready", "output", output)
	i.printSuccess("AI Gateway is ready")
	return nil
}

// applyRedisConfiguration applies the Redis configuration for rate limiting.
func (i *installOrchestrator) applyRedisConfiguration(ctx context.Context) error {
	i.printStep("  🗄️", "Installing Redis for rate limiting...")

	if err := i.applyYAMLFromFile(ctx, redisYAMLPath); err != nil {
		i.printError("Failed to apply Redis configuration")
		return fmt.Errorf("failed to apply Redis configuration: %w", err)
	}

	i.printSuccess("Redis configuration applied")
	i.installedComponents = append(i.installedComponents, "redis")
	return nil
}

// applyEnvoyGatewayConfig applies the Envoy Gateway configuration.
func (i *installOrchestrator) applyEnvoyGatewayConfig(ctx context.Context) error {
	i.printStep("  ⚙️", "Applying Envoy Gateway configuration...")

	if err := i.applyYAMLFromFile(ctx, configYAMLPath); err != nil {
		i.printError("Failed to apply Envoy Gateway configuration")
		return fmt.Errorf("failed to apply Envoy Gateway configuration: %w", err)
	}

	i.printSuccess("Envoy Gateway configuration applied")
	i.installedComponents = append(i.installedComponents, "envoy-gateway-config")
	return nil
}

// applyRBACConfiguration applies the RBAC configuration.
func (i *installOrchestrator) applyRBACConfiguration(ctx context.Context) error {
	i.printStep("  🔐", "Applying RBAC configuration...")

	if err := i.applyYAMLFromFile(ctx, rbacYAMLPath); err != nil {
		i.printError("Failed to apply RBAC configuration")
		return fmt.Errorf("failed to apply RBAC configuration: %w", err)
	}

	i.printSuccess("RBAC configuration applied")
	return nil
}

// applyYAMLFromFile applies a YAML file using kubectl.
func (i *installOrchestrator) applyYAMLFromFile(ctx context.Context, filePath string) error {
	args := []string{"apply", "-f", filePath}

	if i.cmd.DryRun {
		args = append(args, "--dry-run=client")
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("failed to apply %s: %w", filePath, err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("Applied YAML file", "file", filePath, "output", output)
	}

	return nil
}

// restartEnvoyGateway restarts the Envoy Gateway deployment.
func (i *installOrchestrator) restartEnvoyGateway(ctx context.Context) error {
	i.printStep("  🔄", "Restarting Envoy Gateway...")

	args := []string{
		"rollout", "restart",
		"-n", "envoy-gateway-system",
		"deployment/envoy-gateway",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printError("Failed to restart Envoy Gateway")
		return fmt.Errorf("failed to restart Envoy Gateway: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("Envoy Gateway restart initiated", "output", output)
	}

	i.printSuccess("Envoy Gateway restart initiated")
	return nil
}

// waitForEnvoyGatewayReady waits for the Envoy Gateway deployment to be ready.
func (i *installOrchestrator) waitForEnvoyGatewayReady(ctx context.Context) error {
	if i.cmd.DryRun {
		i.printStep("  ⏳", "Would wait for Envoy Gateway to be ready")
		return nil
	}

	i.printStep("  ⏳", "Waiting for Envoy Gateway to be ready...")

	args := []string{
		"wait", "--timeout=2m",
		"-n", "envoy-gateway-system",
		"deployment/envoy-gateway",
		"--for=condition=Available",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printError("Envoy Gateway deployment is not ready")
		return fmt.Errorf("envoy Gateway deployment is not ready: %w", err)
	}

	i.logger.Info("Envoy Gateway deployment ready", "output", output)
	i.printSuccess("Envoy Gateway is ready")
	return nil
}

// verifyAIGatewayPods checks that AI Gateway pods are running.
func (i *installOrchestrator) verifyAIGatewayPods(ctx context.Context) error {
	i.printStep("  🔍", "Checking AI Gateway pods...")

	if i.cmd.DryRun {
		i.printStep("  ⏭️", "Would check AI Gateway pods")
		return nil
	}

	args := []string{
		"get", "pods",
		"-n", i.cmd.Namespace,
		"-l", "app.kubernetes.io/instance=aieg",
		"--no-headers",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printError("Failed to get AI Gateway pods")
		return fmt.Errorf("failed to get AI Gateway pods: %w", err)
	}

	if strings.TrimSpace(output) == "" {
		i.printError("No AI Gateway pods found")
		return fmt.Errorf("no AI Gateway pods found")
	}

	// Check if pods are running.
	lines := strings.Split(strings.TrimSpace(output), "\n")
	runningPods := 0
	for _, line := range lines {
		if strings.Contains(line, "Running") && strings.Contains(line, "1/1") {
			runningPods++
		}
	}

	if runningPods == 0 {
		i.printError("No AI Gateway pods are running")
		return fmt.Errorf("no AI Gateway pods are running")
	}

	i.printSuccess(fmt.Sprintf("AI Gateway pods are running (%d/%d)", runningPods, len(lines)))
	return nil
}

// verifyEnvoyGatewayPods checks that Envoy Gateway pods are running.
func (i *installOrchestrator) verifyEnvoyGatewayPods(ctx context.Context) error {
	i.printStep("  🔍", "Checking Envoy Gateway pods...")

	if i.cmd.DryRun {
		i.printStep("  ⏭️", "Would check Envoy Gateway pods")
		return nil
	}

	args := []string{
		"get", "pods",
		"-n", "envoy-gateway-system",
		"-l", "app.kubernetes.io/instance=eg",
		"--no-headers",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printError("Failed to get Envoy Gateway pods")
		return fmt.Errorf("failed to get Envoy Gateway pods: %w", err)
	}

	if strings.TrimSpace(output) == "" {
		i.printError("No Envoy Gateway pods found")
		return fmt.Errorf("no Envoy Gateway pods found")
	}

	// Check if pods are running.
	lines := strings.Split(strings.TrimSpace(output), "\n")
	runningPods := 0
	for _, line := range lines {
		if strings.Contains(line, "Running") && strings.Contains(line, "1/1") {
			runningPods++
		}
	}

	if runningPods == 0 {
		i.printError("No Envoy Gateway pods are running")
		return fmt.Errorf("no Envoy Gateway pods are running")
	}

	i.printSuccess(fmt.Sprintf("Envoy Gateway pods are running (%d/%d)", runningPods, len(lines)))
	return nil
}

// verifyRedisPods checks that Redis pods are running (if rate limiting is enabled).
func (i *installOrchestrator) verifyRedisPods(ctx context.Context) error {
	i.printStep("  🔍", "Checking Redis pods...")

	if i.cmd.DryRun {
		i.printStep("  ⏭️", "Would check Redis pods")
		return nil
	}

	args := []string{
		"get", "pods",
		"-n", "redis-system",
		"-l", "app=redis",
		"--no-headers",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		i.printWarning("Redis pods not found (rate limiting may not work)")
		i.logger.Warn("Failed to get Redis pods", "error", err)
		return nil // Don't fail the installation for Redis.
	}

	if strings.TrimSpace(output) == "" {
		i.printWarning("No Redis pods found (rate limiting may not work)")
		return nil
	}

	// Check if pods are running.
	lines := strings.Split(strings.TrimSpace(output), "\n")
	runningPods := 0
	for _, line := range lines {
		if strings.Contains(line, "Running") && strings.Contains(line, "1/1") {
			runningPods++
		}
	}

	if runningPods == 0 {
		i.printWarning("Redis pods are not running (rate limiting may not work)")
		return nil
	}

	i.printSuccess(fmt.Sprintf("Redis pods are running (%d/%d)", runningPods, len(lines)))
	return nil
}

// verifyServices checks that services are accessible.
func (i *installOrchestrator) verifyServices(ctx context.Context) error {
	i.printStep("  🔍", "Checking services...")

	if i.cmd.DryRun {
		i.printStep("  ⏭️", "Would check services")
		return nil
	}

	// Check AI Gateway service.
	if err := i.checkService(ctx, i.cmd.Namespace, "ai-gateway-controller"); err != nil {
		i.printWarning("AI Gateway service check failed: " + err.Error())
	} else {
		i.printSuccess("AI Gateway service is accessible")
	}

	// Check Envoy Gateway service.
	if err := i.checkService(ctx, "envoy-gateway-system", "envoy-gateway"); err != nil {
		i.printWarning("Envoy Gateway service check failed: " + err.Error())
	} else {
		i.printSuccess("Envoy Gateway service is accessible")
	}

	return nil
}

// checkService checks if a service exists and is accessible.
func (i *installOrchestrator) checkService(ctx context.Context, namespace, serviceName string) error {
	args := []string{
		"get", "service",
		"-n", namespace,
		serviceName,
		"--no-headers",
	}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("service %s not found in namespace %s", serviceName, namespace)
	}

	if strings.TrimSpace(output) == "" {
		return fmt.Errorf("service %s not found in namespace %s", serviceName, namespace)
	}

	i.logger.Info("Service check", "namespace", namespace, "service", serviceName, "output", output)
	return nil
}

// handleError handles installation errors with optional rollback.
func (i *installOrchestrator) handleError(message string, err error, shouldRollback bool) error {
	i.printError(message)
	i.logger.Error(message, "error", err)

	if shouldRollback && !i.cmd.DryRun {
		i.printWarning("Attempting to rollback installation...")
		if rollbackErr := i.rollbackInstallation(context.Background()); rollbackErr != nil {
			i.printError("Rollback failed: " + rollbackErr.Error())
			i.logger.Error("Rollback failed", "error", rollbackErr)
		} else {
			i.printSuccess("Rollback completed")
		}
	}

	// Provide helpful error messages and suggestions.
	i.printTroubleshootingInfo(err)

	return fmt.Errorf("%s: %w", message, err)
}

// rollbackInstallation attempts to clean up installed components.
func (i *installOrchestrator) rollbackInstallation(ctx context.Context) error {
	i.printStep("🔄", "Rolling back installation...")

	var rollbackErrors []error

	// Rollback in reverse order.
	for idx := len(i.installedComponents) - 1; idx >= 0; idx-- {
		component := i.installedComponents[idx]
		if err := i.rollbackComponent(ctx, component); err != nil {
			rollbackErrors = append(rollbackErrors, fmt.Errorf("failed to rollback %s: %w", component, err))
		}
	}

	if len(rollbackErrors) > 0 {
		return fmt.Errorf("rollback completed with errors: %v", rollbackErrors)
	}

	return nil
}

// rollbackComponent rolls back a specific component.
func (i *installOrchestrator) rollbackComponent(ctx context.Context, component string) error {
	i.printStep("  🗑️", fmt.Sprintf("Removing %s...", component))

	switch component {
	case "envoy-gateway":
		return i.uninstallHelmRelease(ctx, "eg", "envoy-gateway-system")
	case "ai-gateway-controller":
		return i.uninstallHelmRelease(ctx, "aieg", i.cmd.Namespace)
	case "ai-gateway-crds":
		return i.uninstallHelmRelease(ctx, "aieg-crd", i.cmd.Namespace)
	case "redis":
		return i.removeRedis(ctx)
	case "envoy-gateway-config":
		return i.removeEnvoyGatewayConfig(ctx)
	default:
		i.logger.Warn("Unknown component for rollback", "component", component)
		return nil
	}
}

// uninstallHelmRelease uninstalls a Helm release.
func (i *installOrchestrator) uninstallHelmRelease(ctx context.Context, releaseName, namespace string) error {
	args := []string{"uninstall", releaseName, "-n", namespace}

	output, err := i.runCommand(ctx, "helm", args...)
	if err != nil {
		// Don't fail if release doesn't exist.
		if strings.Contains(err.Error(), "not found") {
			return nil
		}
		return err
	}

	i.logger.Info("Helm release uninstalled", "release", releaseName, "output", output)
	return nil
}

// removeRedis removes Redis resources.
func (i *installOrchestrator) removeRedis(ctx context.Context) error {
	args := []string{"delete", "-f", redisYAMLPath, "--ignore-not-found=true"}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return err
	}

	i.logger.Info("Redis resources removed", "output", output)
	return nil
}

// removeEnvoyGatewayConfig removes Envoy Gateway configuration.
func (i *installOrchestrator) removeEnvoyGatewayConfig(ctx context.Context) error {
	// Remove config and RBAC, but don't remove the entire Envoy Gateway.
	args := []string{"delete", "-f", rbacYAMLPath, "--ignore-not-found=true"}

	output, err := i.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return err
	}

	i.logger.Info("Envoy Gateway config removed", "output", output)
	return nil
}

// printTroubleshootingInfo provides helpful troubleshooting information.
func (i *installOrchestrator) printTroubleshootingInfo(err error) {
	fmt.Fprintln(i.stdout)
	i.printStep("💡", "Troubleshooting suggestions:")

	errorStr := err.Error()

	switch {
	case strings.Contains(errorStr, "kubectl"):
		fmt.Fprintln(i.stdout, "  • Ensure kubectl is installed and configured")
		fmt.Fprintln(i.stdout, "  • Check cluster connectivity: kubectl cluster-info")
		fmt.Fprintln(i.stdout, "  • Verify you have sufficient permissions")

	case strings.Contains(errorStr, "helm"):
		fmt.Fprintln(i.stdout, "  • Ensure Helm is installed and configured")
		fmt.Fprintln(i.stdout, "  • Check Helm version: helm version")
		fmt.Fprintln(i.stdout, "  • Try updating Helm repositories: helm repo update")

	case strings.Contains(errorStr, "timeout") || strings.Contains(errorStr, "deadline"):
		fmt.Fprintln(i.stdout, "  • The operation timed out - this may be due to slow network or cluster")
		fmt.Fprintln(i.stdout, "  • Try running the command again")
		fmt.Fprintln(i.stdout, "  • Check cluster resources: kubectl top nodes")

	case strings.Contains(errorStr, "permission") || strings.Contains(errorStr, "forbidden"):
		fmt.Fprintln(i.stdout, "  • Check your Kubernetes permissions")
		fmt.Fprintln(i.stdout, "  • Ensure you have cluster-admin or sufficient RBAC permissions")
		fmt.Fprintln(i.stdout, "  • Try: kubectl auth can-i '*' '*' --all-namespaces")

	case strings.Contains(errorStr, "not found"):
		fmt.Fprintln(i.stdout, "  • The resource was not found - it may not be installed")
		fmt.Fprintln(i.stdout, "  • Check if the namespace exists")
		fmt.Fprintln(i.stdout, "  • Verify the resource name and namespace")

	default:
		fmt.Fprintln(i.stdout, "  • Check the logs for more details")
		fmt.Fprintln(i.stdout, "  • Ensure all prerequisites are met")
		fmt.Fprintln(i.stdout, "  • Try running with --debug for more information")
	}

	fmt.Fprintln(i.stdout)
	fmt.Fprintln(i.stdout, "For more help:")
	fmt.Fprintln(i.stdout, "  • Documentation: https://gateway.envoyproxy.io/ai-gateway/")
	fmt.Fprintln(i.stdout, "  • GitHub Issues: https://github.com/envoyproxy/ai-gateway/issues")
	fmt.Fprintln(i.stdout, "  • Community Slack: https://envoyproxy.slack.com/archives/C07Q4N24VAA")
	fmt.Fprintln(i.stdout)
}
