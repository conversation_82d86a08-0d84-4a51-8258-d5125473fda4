// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package main

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os/exec"
	"strings"
	"time"

	"github.com/fatih/color"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// install implements subCmd[cmdInstall]. This function orchestrates the installation
// of Envoy AI Gateway and configures Envoy Gateway.
func install(ctx context.Context, cmd cmdInstall, stdout, stderr io.Writer) error {
	stderrLogger := slog.New(slog.NewTextHandler(stderr, &slog.HandlerOptions{}))
	if !cmd.Debug {
		stderrLogger = slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{}))
	}

	// Create Kubernetes client
	k8sClient, err := createKubernetesClient()
	if err != nil {
		return fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	// Create installation orchestrator
	installer := &installOrchestrator{
		cmd:       cmd,
		stdout:    stdout,
		stderr:    stderr,
		logger:    stderrLogger,
		k8sClient: k8sClient,
	}

	// Run the installation process
	return installer.run(ctx)
}

// installOrchestrator manages the installation process
type installOrchestrator struct {
	cmd       cmdInstall
	stdout    io.Writer
	stderr    io.Writer
	logger    *slog.Logger
	k8sClient kubernetes.Interface
}

// run executes the installation process
func (i *installOrchestrator) run(ctx context.Context) error {
	if i.cmd.DryRun {
		fmt.Fprintln(i.stdout, "🔍 Dry run mode - no changes will be made")
	}

	// TODO: Implement installation steps
	fmt.Fprintln(i.stdout, "🚀 Starting Envoy AI Gateway installation...")

	// Step 1: Check prerequisites
	if err := i.checkPrerequisites(ctx); err != nil {
		return fmt.Errorf("prerequisite check failed: %w", err)
	}

	// Step 2: Install AI Gateway
	if err := i.installAIGateway(ctx); err != nil {
		return fmt.Errorf("AI Gateway installation failed: %w", err)
	}

	// Step 3: Configure Envoy Gateway
	if err := i.configureEnvoyGateway(ctx); err != nil {
		return fmt.Errorf("Envoy Gateway configuration failed: %w", err)
	}

	// Step 4: Verify installation
	if err := i.verifyInstallation(ctx); err != nil {
		return fmt.Errorf("installation verification failed: %w", err)
	}

	fmt.Fprintln(i.stdout, "✅ Envoy AI Gateway installation completed successfully!")
	return nil
}

// checkPrerequisites verifies that all required tools and conditions are met
func (i *installOrchestrator) checkPrerequisites(ctx context.Context) error {
	i.printStep("📋", "Checking prerequisites...")

	// Check kubectl availability
	if err := i.checkKubectl(ctx); err != nil {
		return err
	}

	// Check cluster connectivity
	if err := i.checkClusterConnectivity(ctx); err != nil {
		return err
	}

	// Check Helm availability
	if err := i.checkHelm(ctx); err != nil {
		return err
	}

	i.printSuccess("Prerequisites check completed")
	i.logger.Info("Prerequisites check completed")
	return nil
}

// installAIGateway installs the AI Gateway using Helm
func (i *installOrchestrator) installAIGateway(ctx context.Context) error {
	fmt.Fprintln(i.stdout, "📦 Installing AI Gateway...")

	// TODO: Install AI Gateway Helm chart
	// TODO: Wait for deployment to be ready

	i.logger.Info("AI Gateway installation completed")
	return nil
}

// configureEnvoyGateway applies the necessary configuration to Envoy Gateway
func (i *installOrchestrator) configureEnvoyGateway(ctx context.Context) error {
	fmt.Fprintln(i.stdout, "⚙️  Configuring Envoy Gateway...")

	// TODO: Apply config.yaml
	// TODO: Apply rbac.yaml
	// TODO: Apply redis.yaml (if not skipped)
	// TODO: Restart Envoy Gateway deployment

	i.logger.Info("Envoy Gateway configuration completed")
	return nil
}

// verifyInstallation checks that all components are running correctly
func (i *installOrchestrator) verifyInstallation(ctx context.Context) error {
	fmt.Fprintln(i.stdout, "🔍 Verifying installation...")

	// TODO: Check AI Gateway pods status
	// TODO: Check Envoy Gateway pods status
	// TODO: Check Redis pods status (if installed)
	// TODO: Verify services are accessible

	i.logger.Info("Installation verification completed")
	return nil
}

// createKubernetesClient creates a Kubernetes client using the default kubeconfig
func createKubernetesClient() (kubernetes.Interface, error) {
	config, err := clientcmd.BuildConfigFromFlags("", clientcmd.RecommendedHomeFile)
	if err != nil {
		return nil, fmt.Errorf("failed to build kubeconfig: %w", err)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	return clientset, nil
}

// runCommand executes a command and returns the output
func (i *installOrchestrator) runCommand(ctx context.Context, name string, args ...string) (string, error) {
	if i.cmd.DryRun {
		i.logger.Info("Dry run: would execute command", "command", name, "args", args)
		return "", nil
	}

	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), fmt.Errorf("command failed: %s %v: %w\nOutput: %s", name, args, err, string(output))
	}
	return string(output), nil
}

// printStep prints a step with an icon and message
func (i *installOrchestrator) printStep(icon, message string) {
	fmt.Fprintf(i.stdout, "%s %s\n", icon, color.CyanString(message))
}

// printSuccess prints a success message
func (i *installOrchestrator) printSuccess(message string) {
	fmt.Fprintf(i.stdout, "✅ %s\n", color.GreenString(message))
}

// printError prints an error message
func (i *installOrchestrator) printError(message string) {
	fmt.Fprintf(i.stdout, "❌ %s\n", color.RedString(message))
}

// printWarning prints a warning message
func (i *installOrchestrator) printWarning(message string) {
	fmt.Fprintf(i.stdout, "⚠️  %s\n", color.YellowString(message))
}

// printProgress prints a progress indicator with animation
func (i *installOrchestrator) printProgress(message string, duration time.Duration) {
	if i.cmd.DryRun {
		i.printStep("⏭️", fmt.Sprintf("Would %s", strings.ToLower(message)))
		return
	}

	spinner := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	start := time.Now()

	for time.Since(start) < duration {
		for _, s := range spinner {
			fmt.Fprintf(i.stdout, "\r%s %s", s, color.BlueString(message))
			time.Sleep(100 * time.Millisecond)
			if time.Since(start) >= duration {
				break
			}
		}
	}

	fmt.Fprintf(i.stdout, "\r✅ %s\n", color.GreenString(message))
}

// checkKubectl verifies that kubectl is available and working
func (i *installOrchestrator) checkKubectl(ctx context.Context) error {
	i.printStep("  🔍", "Checking kubectl...")

	// Check if kubectl is available
	if _, err := exec.LookPath("kubectl"); err != nil {
		i.printError("kubectl not found in PATH")
		return fmt.Errorf("kubectl is required but not found in PATH")
	}

	// Check kubectl version
	output, err := i.runCommand(ctx, "kubectl", "version", "--client", "--output=yaml")
	if err != nil {
		i.printError("Failed to get kubectl version")
		return fmt.Errorf("failed to get kubectl version: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("kubectl version", "output", output)
	}

	i.printSuccess("kubectl is available")
	return nil
}

// checkClusterConnectivity verifies that we can connect to the Kubernetes cluster
func (i *installOrchestrator) checkClusterConnectivity(ctx context.Context) error {
	i.printStep("  🔍", "Checking cluster connectivity...")

	// Try to get cluster info
	_, err := i.runCommand(ctx, "kubectl", "cluster-info")
	if err != nil {
		i.printError("Cannot connect to Kubernetes cluster")
		return fmt.Errorf("failed to connect to Kubernetes cluster: %w", err)
	}

	i.printSuccess("Cluster connectivity verified")
	return nil
}

// checkHelm verifies that Helm is available and working
func (i *installOrchestrator) checkHelm(ctx context.Context) error {
	i.printStep("  🔍", "Checking Helm...")

	// Check if helm is available
	if _, err := exec.LookPath("helm"); err != nil {
		i.printError("Helm not found in PATH")
		return fmt.Errorf("Helm is required but not found in PATH")
	}

	// Check helm version
	output, err := i.runCommand(ctx, "helm", "version", "--short")
	if err != nil {
		i.printError("Failed to get Helm version")
		return fmt.Errorf("failed to get Helm version: %w", err)
	}

	if !i.cmd.DryRun {
		i.logger.Info("Helm version", "output", output)
	}

	i.printSuccess("Helm is available")
	return nil
}
