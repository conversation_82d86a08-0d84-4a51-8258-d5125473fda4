// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package main

import (
	"bytes"
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInstallCommand(t *testing.T) {
	tests := []struct {
		name           string
		cmd            cmdInstall
		expectError    bool
		expectedOutput []string
	}{
		{
			name: "dry run basic install",
			cmd: cmdInstall{
				DryRun:    true,
				Namespace: "test-namespace",
				Version:   "v1.0.0",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Envoy AI Gateway Installer",
				"Checking prerequisites",
				"Installing AI Gateway",
				"Configuring Envoy Gateway",
				"Verifying installation",
				"Installation Complete",
			},
		},
		{
			name: "dry run skip rate limit",
			cmd: cmdInstall{
				DryRun:        true,
				SkipRateLimit: true,
				Namespace:     "test-namespace",
				Version:       "v1.0.0",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Envoy AI Gateway Installer",
				"Installation Complete",
			},
		},
		{
			name: "dry run with custom values",
			cmd: cmdInstall{
				DryRun:    true,
				Namespace: "custom-namespace",
				Version:   "v2.0.0",
				Set:       []string{"key1=value1", "key2=value2"},
				Values:    "/path/to/values.yaml",
			},
			expectError: false,
			expectedOutput: []string{
				"Dry run mode",
				"Installation Complete",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var stdout, stderr bytes.Buffer

			err := install(context.Background(), tt.cmd, &stdout, &stderr)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			output := stdout.String()
			for _, expected := range tt.expectedOutput {
				assert.Contains(t, output, expected, "Expected output to contain: %s", expected)
			}
		})
	}
}

func TestInstallOrchestrator_PrintFunctions(t *testing.T) {
	var stdout, stderr bytes.Buffer

	installer := &installOrchestrator{
		cmd: cmdInstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
	}

	// Test print functions
	installer.printStep("🔍", "Test step")
	installer.printSuccess("Test success")
	installer.printError("Test error")
	installer.printWarning("Test warning")

	output := stdout.String()
	assert.Contains(t, output, "🔍")
	assert.Contains(t, output, "Test step")
	assert.Contains(t, output, "✅")
	assert.Contains(t, output, "Test success")
	assert.Contains(t, output, "❌")
	assert.Contains(t, output, "Test error")
	assert.Contains(t, output, "⚠️")
	assert.Contains(t, output, "Test warning")
}

func TestInstallOrchestrator_RunCommand(t *testing.T) {
	var stdout, stderr bytes.Buffer

	installer := &installOrchestrator{
		cmd: cmdInstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
	}

	// Test dry run command
	output, err := installer.runCommand(context.Background(), "echo", "test")
	assert.NoError(t, err)
	assert.Empty(t, output) // Dry run should return empty output

	// Test actual command (non-dry run)
	installer.cmd.DryRun = false
	output, err = installer.runCommand(context.Background(), "echo", "test")
	assert.NoError(t, err)
	assert.Contains(t, output, "test")
}

func TestInstallOrchestrator_HandleError(t *testing.T) {
	var stdout, stderr bytes.Buffer

	installer := &installOrchestrator{
		cmd: cmdInstall{
			DryRun: true,
		},
		stdout: &stdout,
		stderr: &stderr,
	}

	// Test error handling without rollback
	err := installer.handleError("test error", assert.AnError, false)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "test error")

	output := stdout.String()
	assert.Contains(t, output, "❌")
	assert.Contains(t, output, "test error")
	assert.Contains(t, output, "Troubleshooting suggestions")
}

func TestInstallOrchestrator_TroubleshootingInfo(t *testing.T) {
	var stdout, stderr bytes.Buffer

	installer := &installOrchestrator{
		cmd:    cmdInstall{},
		stdout: &stdout,
		stderr: &stderr,
	}

	tests := []struct {
		name         string
		errorMsg     string
		expectedTips []string
	}{
		{
			name:     "kubectl error",
			errorMsg: "kubectl command failed",
			expectedTips: []string{
				"kubectl is installed",
				"cluster connectivity",
				"sufficient permissions",
			},
		},
		{
			name:     "helm error",
			errorMsg: "helm install failed",
			expectedTips: []string{
				"Helm is installed",
				"helm version",
				"helm repo update",
			},
		},
		{
			name:     "timeout error",
			errorMsg: "operation timeout exceeded",
			expectedTips: []string{
				"timed out",
				"slow network",
				"kubectl top nodes",
			},
		},
		{
			name:     "permission error",
			errorMsg: "permission denied",
			expectedTips: []string{
				"Kubernetes permissions",
				"cluster-admin",
				"kubectl auth can-i",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stdout.Reset()

			testErr := fmt.Errorf(tt.errorMsg)
			installer.printTroubleshootingInfo(testErr)

			output := stdout.String()
			for _, tip := range tt.expectedTips {
				assert.Contains(t, output, tip, "Expected troubleshooting tip: %s", tip)
			}

			// Check for common help links
			assert.Contains(t, output, "Documentation:")
			assert.Contains(t, output, "GitHub Issues:")
			assert.Contains(t, output, "Community Slack:")
		})
	}
}

func TestCreateKubernetesClient(t *testing.T) {
	// This test will only work if there's a valid kubeconfig
	// In CI/CD environments, this might not be available
	client, err := createKubernetesClient()

	if err != nil {
		// If we can't create a client, that's expected in some environments
		t.Skipf("Skipping Kubernetes client test: %v", err)
		return
	}

	require.NotNil(t, client)
}

func TestInstallOrchestrator_ComponentTracking(t *testing.T) {
	installer := &installOrchestrator{
		cmd: cmdInstall{
			DryRun: true,
		},
		installedComponents: []string{},
	}

	// Simulate component installation
	installer.installedComponents = append(installer.installedComponents, "ai-gateway-crds")
	installer.installedComponents = append(installer.installedComponents, "ai-gateway-controller")
	installer.installedComponents = append(installer.installedComponents, "redis")

	assert.Len(t, installer.installedComponents, 3)
	assert.Equal(t, "ai-gateway-crds", installer.installedComponents[0])
	assert.Equal(t, "ai-gateway-controller", installer.installedComponents[1])
	assert.Equal(t, "redis", installer.installedComponents[2])
}
